<template>
  <div class="system-page">
    <div class="page-header">
      <h2>系统设置</h2>
    </div>
    
    <div class="content-card">
      <div class="coming-soon">
        <el-icon size="64" color="#ccc"><Setting /></el-icon>
        <h3>系统管理功能开发中...</h3>
      </div>
    </div>
  </div>
</template>

<script setup>
// 系统管理页面
</script>

<style lang="scss" scoped>
.system-page {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
  }
  
  .coming-soon {
    text-align: center;
    padding: 80px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #666;
    }
    
    p {
      color: #999;
      margin: 0;
    }
  }
}
</style>
