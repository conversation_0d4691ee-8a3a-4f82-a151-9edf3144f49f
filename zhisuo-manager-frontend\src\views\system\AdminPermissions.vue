<template>
  <div class="admin-permissions-page">
    <div class="page-header">
      <h2>管理员与权限</h2>
      <p>管理系统管理员账号与权限配置</p>
    </div>

    <!-- 标签页 -->
    <div class="content-card">
      <el-tabs v-model="activeTab" class="admin-tabs">
        <el-tab-pane label="管理员列表" name="admins">
          <template #label>
            <span class="tab-label">
              <el-icon><UserFilled /></el-icon>
              管理员列表
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="角色管理" name="roles">
          <template #label>
            <span class="tab-label">
              <el-icon><Key /></el-icon>
              角色管理
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="权限配置" name="permissions">
          <template #label>
            <span class="tab-label">
              <el-icon><Lock /></el-icon>
              权限配置
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>

      <!-- 管理员列表内容 -->
      <div v-if="activeTab === 'admins'" class="tab-content">
        <!-- 搜索和操作区域 -->
        <div class="search-section">
          <div class="search-left">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索管理员"
              style="width: 300px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-left: 12px">
              <el-option label="全部" value="" />
              <el-option label="正常" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </div>
          <div class="search-right">
            <el-button type="primary" @click="handleAddAdmin">
              <el-icon><Plus /></el-icon>
              添加管理员
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>

        <!-- 管理员表格 -->
        <div class="table-container">
          <el-table
            :data="adminList"
            v-loading="loading"
            style="width: 100%"
            class="admin-table"
          >
            <el-table-column prop="adminId" label="管理员ID" width="120" />
            <el-table-column prop="realName" label="姓名" width="100" />
            <el-table-column prop="email" label="邮箱/用户名" min-width="200" />
            <el-table-column prop="phone" label="手机号" width="140" />
            <el-table-column prop="role" label="角色" width="120">
              <template #default="{ row }">
                <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
                  {{ row.role === 'admin' ? '超级管理员' : '普通管理员' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '正常' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" size="small" @click="handleEdit(row)">
                  编辑
                </el-button>
                <el-button link type="danger" size="small" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.current"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>

      <!-- 角色管理内容 -->
      <div v-else-if="activeTab === 'roles'" class="tab-content">
        <div class="coming-soon">
          <el-icon size="64" color="#ccc"><Key /></el-icon>
          <h3>角色管理功能开发中...</h3>
        </div>
      </div>

      <!-- 权限配置内容 -->
      <div v-else-if="activeTab === 'permissions'" class="tab-content">
        <div class="coming-soon">
          <el-icon size="64" color="#ccc"><Lock /></el-icon>
          <h3>权限配置功能开发中...</h3>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  UserFilled, 
  Key, 
  Lock, 
  Search, 
  Plus, 
  Refresh 
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('admins')
const searchKeyword = ref('')
const statusFilter = ref('')
const loading = ref(false)
const adminList = ref([])
const pagination = ref({
  current: 1,
  size: 10,
  total: 0
})

// 模拟管理员数据
const mockAdminData = [
  {
    adminId: 'A001',
    realName: '张三',
    email: '<EMAIL>',
    phone: '19870873498',
    role: 'admin',
    status: 1,
    createTime: '2023-05-15 09:30:22'
  },
  {
    adminId: 'A002',
    realName: '李四',
    email: '<EMAIL>',
    phone: '19870873743',
    role: 'manager',
    status: 1,
    createTime: '2023-06-22 14:18:36'
  },
  {
    adminId: 'A003',
    realName: '王五',
    email: '<EMAIL>',
    phone: '15879036849',
    role: 'manager',
    status: 1,
    createTime: '2023-07-08 11:42:15'
  },
  {
    adminId: 'A004',
    realName: '赵六',
    email: '<EMAIL>',
    phone: '19879087864',
    role: 'manager',
    status: 0,
    createTime: '2023-08-30 16:05:47'
  },
  {
    adminId: 'A005',
    realName: '钱七',
    email: '<EMAIL>',
    phone: '15697868905',
    role: 'manager',
    status: 1,
    createTime: '2023-09-12 10:26:33'
  }
]

// 获取管理员列表
const fetchAdminList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    adminList.value = mockAdminData
    pagination.value.total = mockAdminData.length
  } catch (error) {
    ElMessage.error('获取管理员列表失败')
  } finally {
    loading.value = false
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.value.size = size
  fetchAdminList()
}

// 处理当前页变化
const handleCurrentChange = (current) => {
  pagination.value.current = current
  fetchAdminList()
}

// 添加管理员
const handleAddAdmin = () => {
  ElMessage.info('添加管理员功能开发中...')
}

// 编辑管理员
const handleEdit = (row) => {
  ElMessage.info('编辑管理员功能开发中...')
}

// 删除管理员
const handleDelete = (row) => {
  ElMessage.info('删除管理员功能开发中...')
}

// 刷新列表
const handleRefresh = () => {
  fetchAdminList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAdminList()
})
</script>

<style lang="scss" scoped>
.admin-permissions-page {
  padding: 24px;
  
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #666;
      margin: 0;
      font-size: 14px;
    }
  }
  
  .content-card {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .admin-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 24px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #f0f0f0;
    }

    .tab-label {
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .tab-content {
    min-height: 400px;
  }

  .search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .search-left {
      display: flex;
      align-items: center;
    }
    
    .search-right {
      display: flex;
      gap: 12px;
    }
  }

  .table-container {
    .admin-table {
      border-radius: 8px;
      overflow: hidden;
      
      :deep(.el-table__header) {
        background-color: #f8fafc;
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .coming-soon {
    text-align: center;
    padding: 80px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #666;
    }
  }
}
</style>
